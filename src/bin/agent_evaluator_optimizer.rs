//! 代理评估优化器

use rig::completion::Prompt;
use rig::providers::openai;
use rig::prelude::*;
use schemars::JsonSchema;
use serde::{Deserialize, Serialize};

#[derive(Debug, Serialize, Deserialize, JsonSchema)]
struct Evaluation {
    evaluation_status: EvalStatus,
    feedback:String,
}

#[derive(Debug, Serialize, Deserialize, JsonSchema, PartialEq)]
enum EvalStatus {
    Pass,
    NeedsImprovement,
    Fail,
}

const TASK: &str = "Implement a Stack with:
1. push(x)
2. pop()
3. getMin()
All operations should be O(1).
";

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let model_name = "qwen-max";
    let api_key = "sk-38a06663ea9f41448b710c19e9563d13";
    let base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    let client = openai::Client::from_url(api_key, base_url);

    let generator_agent = client.agent(model_name)
        .preamble(
            "
            Your goal is to complete the task based on <user input>. If there are feedback
            from your previous generations, you should reflect on them to improve your solution

            Output your answer concisely in the following format:

            Thoughts:
            [Your understanding of the task and feedback and how you plan to improve]

            Response:
            [Your code implementation here]
            "
        )
        .build();
    
    let evaluator_agent = client.extractor::<Evaluation>(model_name)
        .preamble("
        Evaluate this following code implementation for:
            1. code correctness
            2. time complexity
            3. style and best practices

            You should be evaluating only and not attempting to solve the task.

            Only output \"PASS\" if all criteria are met and you have no further suggestions for improvements.

            Provide detailed feedback if there are areas that need improvement. You should specify what needs improvement and why.

            Only output JSON.
        ")
        .build();
    
    let mut memories = vec![];
    let mut response = generator_agent.prompt(TASK).await?;
    memories.push(response.clone());
    
    loop {
        
        let eval_result = evaluator_agent
            .extract(&format!("{TASK}\n\n{response}"))
            .await?;
        
        if eval_result.evaluation_status == EvalStatus::Pass {
            break;
        } else {
            let context = format!("{TASK}\n\n{}", eval_result.feedback);
            response = generator_agent.prompt(context).await?;
            memories.push(response.clone());
        }
    }
    
    println!("Memories: {:#?}", memories);
    println!("Response: {response}");
    
    Ok(())
}