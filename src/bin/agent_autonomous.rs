use rig::prelude::*;
use rig::providers::openai;
use schemars::JsonSchema;
use std::time::Duration;

#[derive(Debug, serde::Serialize, serde::Deserialize, JsonSchema)]
struct Counter {
    number: u32,
}

#[tokio::main]
async fn main() -> anyhow::Result<()> {
    let model_name = "qwen-max";
    let api_key = "sk-38a06663ea9f41448b710c19e9563d13";
    let base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1";
    let client = openai::Client::from_url(api_key, base_url);

    let agent = client.extractor::<Counter>(model_name)
        .preamble("Your role is to add a random number between 1 and 64 (using only integers) to the previous number.")
        .build();

    let mut number: u32 = 0;
    let mut interval = tokio::time::interval(Duration::from_secs(1));

    loop {
        let response = agent.extract(&number.to_string()).await?;
        println!("Counter: {response:?}");
        if response.number >= 2000 {
            break;
        } else {
            number += response.number;
        }
        interval.tick().await;
    }

    println!("Finished with number: {number}");
    
    Ok(())
}
