{"rustc": 15497389221046826682, "features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"visit\", \"visit-mut\"]", "declared_features": "[\"clone-impls\", \"default\", \"derive\", \"extra-traits\", \"fold\", \"full\", \"parsing\", \"printing\", \"proc-macro\", \"test\", \"visit\", \"visit-mut\"]", "target": 9442126953582868550, "profile": 3033921117576893, "path": 4364566461330718355, "deps": [[1988483478007900009, "unicode_ident", false, 6815120117468702754], [3060637413840920116, "proc_macro2", false, 15073113276356521080], [17990358020177143287, "quote", false, 1378691523734667401]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/syn-a8c27eddbe6f49a8/dep-lib-syn", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}