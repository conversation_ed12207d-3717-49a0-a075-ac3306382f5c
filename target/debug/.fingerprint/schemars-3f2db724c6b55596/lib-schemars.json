{"rustc": 15497389221046826682, "features": "[\"default\", \"derive\", \"schemars_derive\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 8276155916380437441, "path": 7530339676230562730, "deps": [[6913375703034175521, "build_script_build", false, 164965406826652901], [9122563107207267705, "dyn_clone", false, 1304496403401437480], [9689903380558560274, "serde", false, 8066098904840365995], [15367738274754116744, "serde_json", false, 6095436004809659935], [16071897500792579091, "schemars_derive", false, 5458618616228294593]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-3f2db724c6b55596/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}