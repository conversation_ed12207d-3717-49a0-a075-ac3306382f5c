{"rustc": 15497389221046826682, "features": "[\"default\", \"derive\", \"schemars_derive\"]", "declared_features": "[\"arrayvec\", \"arrayvec05\", \"arrayvec07\", \"bigdecimal\", \"bigdecimal03\", \"bigdecimal04\", \"bytes\", \"chrono\", \"default\", \"derive\", \"derive_json_schema\", \"either\", \"enumset\", \"impl_json_schema\", \"indexmap\", \"indexmap1\", \"indexmap2\", \"preserve_order\", \"raw_value\", \"rust_decimal\", \"schemars_derive\", \"semver\", \"smallvec\", \"smol_str\", \"ui_test\", \"url\", \"uuid\", \"uuid08\", \"uuid1\"]", "target": 11155677158530064643, "profile": 5347358027863023418, "path": 7530339676230562730, "deps": [[6913375703034175521, "build_script_build", false, 164965406826652901], [9122563107207267705, "dyn_clone", false, 14041107977434901314], [9689903380558560274, "serde", false, 15106735119383794838], [15367738274754116744, "serde_json", false, 6733725344978976799], [16071897500792579091, "schemars_derive", false, 5458618616228294593]], "local": [{"CheckDepInfo": {"dep_info": "debug/.fingerprint/schemars-f66f708062f82a68/dep-lib-schemars", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}